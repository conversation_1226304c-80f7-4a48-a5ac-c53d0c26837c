events {
    worker_connections 1024;
}

http {
    upstream autogen_chat_api {
        server autogen-chat-api:8000;
    }

    server {
        listen 80;
        server_name localhost;

        # Increase client max body size for large requests
        client_max_body_size 10M;

        # Proxy settings
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # API endpoints
        location / {
            proxy_pass http://autogen_chat_api;
            proxy_read_timeout 300s;
            proxy_connect_timeout 75s;
        }

        # Special handling for streaming endpoints
        location /chat/stream {
            proxy_pass http://autogen_chat_api;
            proxy_buffering off;
            proxy_cache off;
            proxy_read_timeout 300s;
            proxy_connect_timeout 75s;
            
            # Enable streaming
            proxy_set_header Connection '';
            proxy_http_version 1.1;
            chunked_transfer_encoding off;
        }

        # Health check
        location /health {
            proxy_pass http://autogen_chat_api;
            access_log off;
        }
    }
}
