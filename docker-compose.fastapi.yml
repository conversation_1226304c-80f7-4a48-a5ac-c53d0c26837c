version: '3.8'

services:
  autogen-chat-api:
    build:
      context: .
      dockerfile: Dockerfile.fastapi
    ports:
      - "8000:8000"
    environment:
      - PYTHONPATH=/app
    volumes:
      # Mount database directory if needed
      - ~/.autogenstudio:/home/<USER>/.autogenstudio:ro
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Add nginx reverse proxy
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - autogen-chat-api
    restart: unless-stopped
    profiles:
      - with-nginx

networks:
  default:
    name: autogen-chat-network
