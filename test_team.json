{"provider": "autogen_agentchat.teams.RoundRobinGroupChat", "component_type": "team", "version": 1, "component_version": 1, "description": "A single AssistantAgent (with a calculator tool) in a RoundRobinGroupChat team. ", "label": "MyTestTeam", "config": {"participants": [{"provider": "autogen_agentchat.agents.AssistantAgent", "component_type": "agent", "version": 2, "component_version": 2, "description": "An agent that provides assistance with ability to use tools.", "label": "AssistantAgent", "config": {"name": "assistant_agent", "model_client": {"provider": "autogen_ext.models.openai.OpenAIChatCompletionClient", "component_type": "model", "version": 1, "component_version": 1, "description": "Chat completion client for OpenAI hosted models.", "label": "OpenAIChatCompletionClient", "config": {"model": "gpt-4o-mini"}}, "workbench": [{"provider": "autogen_core.tools.StaticStreamWorkbench", "component_type": "workbench", "version": 1, "component_version": 1, "description": "A workbench that provides a static set of tools that do not change after\n    each tool execution, and supports streaming results.", "label": "StaticStreamWorkbench", "config": {"tools": [{"provider": "autogen_core.tools.FunctionTool", "component_type": "tool", "version": 1, "component_version": 1, "description": "Create custom tools by wrapping standard Python functions.", "label": "FunctionTool", "config": {"source_code": "def calculator(a: float, b: float, operator: str) -> str:\n    try:\n        if operator == \"+\":\n            return str(a + b)\n        elif operator == \"-\":\n            return str(a - b)\n        elif operator == \"*\":\n            return str(a * b)\n        elif operator == \"/\":\n            if b == 0:\n                return \"Error: Division by zero\"\n            return str(a / b)\n        else:\n            return \"Error: Invalid operator. Please use +, -, *, or /\"\n    except Exception as e:\n        return f\"Error: {str(e)}\"\n", "name": "calculator", "description": "A simple calculator that performs basic arithmetic operations", "global_imports": [], "has_cancellation_support": false}}], "tool_overrides": {}}}], "model_context": {"provider": "autogen_core.model_context.UnboundedChatCompletionContext", "component_type": "chat_completion_context", "version": 1, "component_version": 1, "description": "An unbounded chat completion context that keeps a view of the all the messages.", "label": "UnboundedChatCompletionContext", "config": {}}, "description": "An agent that provides assistance with ability to use tools.", "system_message": "You are a helpful assistant. Solve tasks carefully. When done, say TERMINATE.", "model_client_stream": false, "reflect_on_tool_use": false, "tool_call_summary_format": "{result}", "max_tool_iterations": 1, "metadata": {}}}, {"provider": "autogen_ext.agents.web_surfer.MultimodalWebSurfer", "component_type": "agent", "version": 1, "component_version": 1, "description": "An agent that solves tasks by browsing the web using a headless browser.", "label": "Web Surfer Agent", "config": {"name": "websurfer_agent", "model_client": {"provider": "autogen_ext.models.openai.OpenAIChatCompletionClient", "component_type": "model", "version": 1, "component_version": 1, "description": "Chat completion client for OpenAI hosted models.", "label": "OpenAIChatCompletionClient", "config": {"model": "gpt-4o-mini"}}, "description": "an agent that solves tasks by browsing the web", "headless": true, "start_page": "https://www.bing.com/", "animate_actions": false, "to_save_screenshots": false, "use_ocr": false, "to_resize_viewport": true}}], "termination_condition": {"provider": "autogen_agentchat.base.AndTerminationCondition", "component_type": "termination", "version": 1, "component_version": 1, "description": "Complex termination: (token usage OR timeout) AND (text mention 'TERMINATE' OR stop message).", "label": "Complex Termination", "config": {"conditions": [{"provider": "autogen_agentchat.base.OrTerminationCondition", "component_type": "termination", "version": 1, "component_version": 1, "label": "OrTerminationCondition", "config": {"conditions": [{"provider": "autogen_agentchat.conditions.TokenUsageTermination", "component_type": "termination", "version": 1, "component_version": 1, "description": "Terminate the conversation if a token usage limit is reached.", "label": "TokenUsageTermination", "config": {"max_total_token": 1000, "max_prompt_token": 800, "max_completion_token": 200}}, {"provider": "autogen_agentchat.conditions.TimeoutTermination", "component_type": "termination", "version": 1, "component_version": 1, "description": "Terminate the conversation after a specified duration has passed.", "label": "TimeoutTermination", "config": {"timeout_seconds": 300}}]}}, {"provider": "autogen_agentchat.base.OrTerminationCondition", "component_type": "termination", "version": 1, "component_version": 1, "label": "OrTerminationCondition", "config": {"conditions": [{"provider": "autogen_agentchat.conditions.TextMentionTermination", "component_type": "termination", "version": 1, "component_version": 1, "description": "Terminate the conversation if a specific text is mentioned.", "label": "TextMentionTermination", "config": {"text": "TERMINATE"}}, {"provider": "autogen_agentchat.conditions.StopMessageTermination", "component_type": "termination", "version": 1, "component_version": 1, "description": "Terminate the conversation if a StopMessage is received.", "label": "StopMessageTermination", "config": {}}]}}]}}, "emit_team_events": false}}