#!/usr/bin/env python3
"""
Startup script for AutoGen Studio FastAPI Chat Service
"""

import argparse
import sys
from pathlib import Path

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from fastapi_chat_service import run_server


def main():
    """Main function to start the server with command line arguments"""
    parser = argparse.ArgumentParser(
        description="Start AutoGen Studio FastAPI Chat Service"
    )
    
    parser.add_argument(
        "--host",
        type=str,
        default="0.0.0.0",
        help="Host to bind to (default: 0.0.0.0)"
    )
    
    parser.add_argument(
        "--port",
        type=int,
        default=8000,
        help="Port to bind to (default: 8000)"
    )
    
    parser.add_argument(
        "--reload",
        action="store_true",
        help="Enable auto-reload for development"
    )
    
    parser.add_argument(
        "--no-reload",
        action="store_true",
        help="Disable auto-reload (production mode)"
    )
    
    args = parser.parse_args()
    
    # Determine reload setting
    if args.no_reload:
        reload = False
    elif args.reload:
        reload = True
    else:
        # Default to reload in development
        reload = True
    
    print(f"Starting AutoGen Studio FastAPI Chat Service...")
    print(f"Host: {args.host}")
    print(f"Port: {args.port}")
    print(f"Reload: {reload}")
    print(f"Access the API at: http://{args.host}:{args.port}")
    print(f"API documentation at: http://{args.host}:{args.port}/docs")
    print(f"Alternative docs at: http://{args.host}:{args.port}/redoc")
    
    try:
        run_server(host=args.host, port=args.port, reload=reload)
    except KeyboardInterrupt:
        print("\nShutting down server...")
    except Exception as e:
        print(f"Error starting server: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
