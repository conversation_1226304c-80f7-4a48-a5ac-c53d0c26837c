# defines how core data types in autogenstudio are serialized and stored in the database

from datetime import datetime
from typing import Optional, Union

from autogen_core import ComponentModel

from sqlmodel import JSON, Column, DateTime, Field, SQLModel, func



class BaseDBModel(SQLModel, table=False):
    """
    Base model with common fields for all database tables.
    Not a table itself - meant to be inherited by concrete model classes.
    """

    __abstract__ = True

    # Common fields present in all database tables
    id: Optional[int] = Field(default=None, primary_key=True)

    created_at: datetime = Field(
        default_factory=datetime.now,
        sa_type=DateTime(timezone=True),  # type: ignore[assignment]
        sa_column_kwargs={"server_default": func.now(), "nullable": True},
    )

    updated_at: datetime = Field(
        default_factory=datetime.now,
        sa_type=DateTime(timezone=True),  # type: ignore[assignment]
        sa_column_kwargs={"onupdate": func.now(), "nullable": True},
    )

    user_id: Optional[str] = None
    version: Optional[str] = "0.0.1"


class Team(BaseDBModel, table=True):
    __table_args__ = {"sqlite_autoincrement": True}
    component: Union[ComponentModel, dict] = Field(sa_column=Column(JSON))

