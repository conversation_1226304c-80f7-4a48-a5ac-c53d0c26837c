# AutoGen Studio Chat API 接口文档

## 概述

AutoGen Studio Chat API 是一个基于 FastAPI 的聊天服务，提供了与 AutoGen Studio 聊天后端的 REST API 接口。该服务支持流式和非流式聊天响应，并支持多种模型类型。

## 基本信息

- **服务名称**: AutoGen Studio Chat API
- **版本**: 1.0.0
- **基础URL**: `http://localhost:8000`
- **协议**: HTTP/HTTPS

## 数据模型

### Message (消息模型)

```json
{
  "type": "string",      // 消息类型: "text" 或 "image"
  "content": "string",   // 消息内容
  "role": "string"       // 消息角色: "user", "assistant", "system"
}
```

### ChatRequest (聊天请求模型)

```json
{
  "messages": [Message],        // 对话消息列表 (必需)
  "model_name": "string",       // 模型名称 (必需)
  "model_type": "string",       // 模型类型: "ollama" 或 "openai" (必需)
  "api_key": "string",          // API密钥 (可选)
  "openai_base_url": "string",         // 模型API基础URL (可选)
  "stream": boolean,            // 是否流式响应 (默认: true)
  "mcp_url_list": ["string"],   // MCP服务器URL列表 (默认: [])
  "team_id": integer            // 团队ID (可选)
}
```

### ChatResponse (聊天响应模型)

```json
{
  "type": "string",      // 响应类型
  "content": "string",   // 响应内容
  "source": "string"     // 响应来源 (可选)
}
```

## API 端点

### 1. 根端点

**GET** `/`

获取API基本信息和可用端点列表。

**响应示例:**
```json
{
  "message": "AutoGen Studio Chat API",
  "version": "1.0.0",
  "endpoints": {
    "chat": "/chat",
    "chat_stream": "/chat/stream",
    "health": "/health"
  }
}
```

### 2. 健康检查

**GET** `/health`

检查服务健康状态。

**响应示例:**
```json
{
  "status": "healthy",
  "service": "autogen-studio-chat-api"
}
```

### 3. 非流式聊天

**POST** `/chat`

发送聊天请求并获取完整响应。

**请求体:**
```json
{
  "messages": [
    {
      "type": "text",
      "content": "你好，请介绍一下你自己",
      "role": "user"
    }
  ],
  "model_name": "gpt-3.5-turbo",
  "model_type": "openai",
  "api_key": "your-api-key",
  "stream": false
}
```

**响应示例:**
```json
{
  "type": "result",
  "content": "你好！我是一个AI助手...",
  "source": null
}
```

**错误响应:**
```json
{
  "detail": "Chat processing failed: 错误详情"
}
```

### 4. 流式聊天

**POST** `/chat/stream`

发送聊天请求并获取流式响应（Server-Sent Events）。

**请求体:** 与非流式聊天相同

**响应格式:** `text/event-stream`

**流式响应示例:**
```
data: {"type": "message", "content": "你好"}

data: {"type": "message", "content": "！我是"}

data: {"type": "message", "content": "一个AI助手"}

data: {"type": "end"}
```

**错误流响应:**
```
data: {"type": "error", "content": "Stream processing failed: 错误详情"}
```

## 使用示例

### Python 客户端示例

```python
import requests
import json

# 非流式请求
def chat_request():
    url = "http://localhost:8000/chat"
    payload = {
        "messages": [
            {
                "type": "text",
                "content": "解释一下机器学习",
                "role": "user"
            }
        ],
        "model_name": "gpt-3.5-turbo",
        "model_type": "openai",
        "api_key": "your-api-key"
    }
    
    response = requests.post(url, json=payload)
    return response.json()

# 流式请求
def chat_stream_request():
    url = "http://localhost:8000/chat/stream"
    payload = {
        "messages": [
            {
                "type": "text",
                "content": "写一首关于春天的诗",
                "role": "user"
            }
        ],
        "model_name": "gpt-3.5-turbo",
        "model_type": "openai",
        "stream": True
    }
    
    response = requests.post(url, json=payload, stream=True)
    for line in response.iter_lines():
        if line:
            line = line.decode('utf-8')
            if line.startswith('data: '):
                data = json.loads(line[6:])
                print(data)
```

### JavaScript 客户端示例

```javascript
// 非流式请求
async function chatRequest() {
    const response = await fetch('http://localhost:8000/chat', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            messages: [{
                type: 'text',
                content: '你好世界',
                role: 'user'
            }],
            model_name: 'gpt-3.5-turbo',
            model_type: 'openai'
        })
    });
    
    return await response.json();
}

// 流式请求
async function chatStreamRequest() {
    const response = await fetch('http://localhost:8000/chat/stream', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            messages: [{
                type: 'text',
                content: '讲个笑话',
                role: 'user'
            }],
            model_name: 'gpt-3.5-turbo',
            model_type: 'openai',
            stream: true
        })
    });
    
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    
    while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        
        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');
        
        for (const line of lines) {
            if (line.startsWith('data: ')) {
                const data = JSON.parse(line.slice(6));
                console.log(data);
            }
        }
    }
}
```

## 错误处理

### HTTP 状态码

- **200**: 请求成功
- **422**: 请求参数验证失败
- **500**: 服务器内部错误

### 错误响应格式

```json
{
  "error": "Internal server error",
  "detail": "具体错误信息",
  "path": "请求路径"
}
```

## 部署和运行

### 启动服务

```bash
# 开发模式（自动重载）
python fastapi_chat_service.py

# 生产模式
uvicorn fastapi_chat_service:app --host 0.0.0.0 --port 8000

# 指定配置
uvicorn fastapi_chat_service:app --host 127.0.0.1 --port 8080 --workers 4
```

### 环境要求

- Python 3.7+
- FastAPI
- Uvicorn
- Pydantic

### 配置说明

- **host**: 服务绑定的主机地址（默认: 0.0.0.0）
- **port**: 服务端口（默认: 8000）
- **reload**: 开发模式自动重载（默认: False）

## 注意事项

1. **API密钥安全**: 请妥善保管API密钥，避免在客户端代码中硬编码
2. **流式响应**: 流式端点使用Server-Sent Events，客户端需要正确处理事件流
3. **错误处理**: 建议在客户端实现适当的错误处理和重试机制
4. **并发限制**: 根据后端模型服务的能力合理控制并发请求数量
5. **超时设置**: 对于长时间运行的请求，建议设置适当的超时时间

## 更新日志

### v1.0.0
- 初始版本发布
- 支持非流式和流式聊天接口
- 支持多种模型类型（OpenAI、Ollama）
- 提供健康检查和基本信息接口
