{"license": "\n                                 Apache License\n                           Version 2.0, January 2004\n                        http://www.apache.org/licenses/\n\n   TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION\n\n   1. Definitions.\n\n      \"License\" shall mean the terms and conditions for use, reproduction,\n      and distribution as defined by Sections 1 through 9 of this document.\n\n      \"Licensor\" shall mean the copyright owner or entity authorized by\n      the copyright owner that is granting the License.\n\n      \"Legal Entity\" shall mean the union of the acting entity and all\n      other entities that control, are controlled by, or are under common\n      control with that entity. For the purposes of this definition,\n      \"control\" means (i) the power, direct or indirect, to cause the\n      direction or management of such entity, whether by contract or\n      otherwise, or (ii) ownership of fifty percent (50%) or more of the\n      outstanding shares, or (iii) beneficial ownership of such entity.\n\n      \"You\" (or \"Your\") shall mean an individual or Legal Entity\n      exercising permissions granted by this License.\n\n      \"Source\" form shall mean the preferred form for making modifications,\n      including but not limited to software source code, documentation\n      source, and configuration files.\n\n      \"Object\" form shall mean any form resulting from mechanical\n      transformation or translation of a Source form, including but\n      not limited to compiled object code, generated documentation,\n      and conversions to other media types.\n\n      \"Work\" shall mean the work of authorship, whether in Source or\n      Object form, made available under the License, as indicated by a\n      copyright notice that is included in or attached to the work\n      (an example is provided in the Appendix below).\n\n      \"Derivative Works\" shall mean any work, whether in Source or Object\n      form, that is based on (or derived from) the Work and for which the\n      editorial revisions, annotations, elaborations, or other modifications\n      represent, as a whole, an original work of authorship. For the purposes\n      of this License, Derivative Works shall not include works that remain\n      separable from, or merely link (or bind by name) to the interfaces of,\n      the Work and Derivative Works thereof.\n\n      \"Contribution\" shall mean any work of authorship, including\n      the original version of the Work and any modifications or additions\n      to that Work or Derivative Works thereof, that is intentionally\n      submitted to Licensor for inclusion in the Work by the copyright owner\n      or by an individual or Legal Entity authorized to submit on behalf of\n      the copyright owner. For the purposes of this definition, \"submitted\"\n      means any form of electronic, verbal, or written communication sent\n      to the Licensor or its representatives, including but not limited to\n      communication on electronic mailing lists, source code control systems,\n      and issue tracking systems that are managed by, or on behalf of, the\n      Licensor for the purpose of discussing and improving the Work, but\n      excluding communication that is conspicuously marked or otherwise\n      designated in writing by the copyright owner as \"Not a Contribution.\"\n\n      \"Contributor\" shall mean Licensor and any individual or Legal Entity\n      on behalf of whom a Contribution has been received by Licensor and\n      subsequently incorporated within the Work.\n\n   2. Grant of Copyright License. Subject to the terms and conditions of\n      this License, each Contributor hereby grants to You a perpetual,\n      worldwide, non-exclusive, no-charge, royalty-free, irrevocable\n      copyright license to reproduce, prepare Derivative Works of,\n      publicly display, publicly perform, sublicense, and distribute the\n      Work and such Derivative Works in Source or Object form.\n\n   3. Grant of Patent License. Subject to the terms and conditions of\n      this License, each Contributor hereby grants to You a perpetual,\n      worldwide, non-exclusive, no-charge, royalty-free, irrevocable\n      (except as stated in this section) patent license to make, have made,\n      use, offer to sell, sell, import, and otherwise transfer the Work,\n      where such license applies only to those patent claims licensable\n      by such Contributor that are necessarily infringed by their\n      Contribution(s) alone or by combination of their Contribution(s)\n      with the Work to which such Contribution(s) was submitted. If You\n      institute patent litigation against any entity (including a\n      cross-claim or counterclaim in a lawsuit) alleging that the Work\n      or a Contribution incorporated within the Work constitutes direct\n      or contributory patent infringement, then any patent licenses\n      granted to You under this License for that Work shall terminate\n      as of the date such litigation is filed.\n\n   4. Redistribution. You may reproduce and distribute copies of the\n      Work or Derivative Works thereof in any medium, with or without\n      modifications, and in Source or Object form, provided that You\n      meet the following conditions:\n\n      (a) You must give any other recipients of the Work or\n          Derivative Works a copy of this License; and\n\n      (b) You must cause any modified files to carry prominent notices\n          stating that You changed the files; and\n\n      (c) You must retain, in the Source form of any Derivative Works\n          that You distribute, all copyright, patent, trademark, and\n          attribution notices from the Source form of the Work,\n          excluding those notices that do not pertain to any part of\n          the Derivative Works; and\n\n      (d) If the Work includes a \"NOTICE\" text file as part of its\n          distribution, then any Derivative Works that You distribute must\n          include a readable copy of the attribution notices contained\n          within such NOTICE file, excluding those notices that do not\n          pertain to any part of the Derivative Works, in at least one\n          of the following places: within a NOTICE text file distributed\n          as part of the Derivative Works; within the Source form or\n          documentation, if provided along with the Derivative Works; or,\n          within a display generated by the Derivative Works, if and\n          wherever such third-party notices normally appear. The contents\n          of the NOTICE file are for informational purposes only and\n          do not modify the License. You may add Your own attribution\n          notices within Derivative Works that You distribute, alongside\n          or as an addendum to the NOTICE text from the Work, provided\n          that such additional attribution notices cannot be construed\n          as modifying the License.\n\n      You may add Your own copyright statement to Your modifications and\n      may provide additional or different license terms and conditions\n      for use, reproduction, or distribution of Your modifications, or\n      for any such Derivative Works as a whole, provided Your use,\n      reproduction, and distribution of the Work otherwise complies with\n      the conditions stated in this License.\n\n   5. Submission of Contributions. Unless You explicitly state otherwise,\n      any Contribution intentionally submitted for inclusion in the Work\n      by You to the Licensor shall be under the terms and conditions of\n      this License, without any additional terms or conditions.\n      Notwithstanding the above, nothing herein shall supersede or modify\n      the terms of any separate license agreement you may have executed\n      with Licensor regarding such Contributions.\n\n   6. Trademarks. This License does not grant permission to use the trade\n      names, trademarks, service marks, or product names of the Licensor,\n      except as required for reasonable and customary use in describing the\n      origin of the Work and reproducing the content of the NOTICE file.\n\n   7. Disclaimer of Warranty. Unless required by applicable law or\n      agreed to in writing, Licensor provides the Work (and each\n      Contributor provides its Contributions) on an \"AS IS\" BASIS,\n      WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or\n      implied, including, without limitation, any warranties or conditions\n      of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A\n      PARTICULAR PURPOSE. You are solely responsible for determining the\n      appropriateness of using or redistributing the Work and assume any\n      risks associated with Your exercise of permissions under this License.\n\n   8. Limitation of Liability. In no event and under no legal theory,\n      whether in tort (including negligence), contract, or otherwise,\n      unless required by applicable law (such as deliberate and grossly\n      negligent acts) or agreed to in writing, shall any Contributor be\n      liable to You for damages, including any direct, indirect, special,\n      incidental, or consequential damages of any character arising as a\n      result of this License or out of the use or inability to use the\n      Work (including but not limited to damages for loss of goodwill,\n      work stoppage, computer failure or malfunction, or any and all\n      other commercial damages or losses), even if such Contributor\n      has been advised of the possibility of such damages.\n\n   9. Accepting Warranty or Additional Liability. While redistributing\n      the Work or Derivative Works thereof, You may choose to offer,\n      and charge a fee for, acceptance of support, warranty, indemnity,\n      or other liability obligations and/or rights consistent with this\n      License. However, in accepting such obligations, You may act only\n      on Your own behalf and on Your sole responsibility, not on behalf\n      of any other Contributor, and only if You agree to indemnify,\n      defend, and hold each Contributor harmless for any liability\n      incurred by, or claims asserted against, such Contributor by reason\n      of your accepting any such warranty or additional liability.\n\n   END OF TERMS AND CONDITIONS\n\n   APPENDIX: How to apply the Apache License to your work.\n\n      To apply the Apache License to your work, attach the following\n      boilerplate notice, with the fields enclosed by brackets \"[]\"\n      replaced with your own identifying information. (Don't include\n      the brackets!)  The text should be enclosed in the appropriate\n      comment syntax for the file format. We also recommend that a\n      file or class name and description of purpose be included on the\n      same \"printed page\" as the copyright notice for easier\n      identification within third-party archives.\n\n   Copyright 2024 Alibaba Cloud\n\n   Licensed under the Apache License, Version 2.0 (the \"License\");\n   you may not use this file except in compliance with the License.\n   You may obtain a copy of the License at\n\n       http://www.apache.org/licenses/LICENSE-2.0\n\n   Unless required by applicable law or agreed to in writing, software\n   distributed under the License is distributed on an \"AS IS\" BASIS,\n   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n   See the License for the specific language governing permissions and\n   limitations under the License.", "modelfile": "# Modelfile generated by \"ollama show\"\n# To build a new Modelfile based on this, replace FROM with:\n# FROM qwen2.5vl:latest\n\nFROM C:\\Users\\<USER>\\.ollama\\models\\blobs\\sha256-a99b7f834d754b88f122d865f32758ba9f0994a83f8363df2c1e71c17605a025\nTEMPLATE \"\"\"{{- if .System -}}\n<|im_start|>system\n{{ .System }}<|im_end|>\n{{- end -}}\n{{- range $i, $_ := .Messages }}\n{{- $last := eq (len (slice $.Messages $i)) 1 -}}\n{{- if eq .Role \"user\" }}\n<|im_start|>user\n{{ .Content }}<|im_end|>\n{{- else if eq .Role \"assistant\" }}\n<|im_start|>assistant\n{{ if .Content }}{{ .Content }}{{ if not $last }}<|im_end|>\n{{- else -}}<|im_end|>{{- end -}}\n{{- end -}}\n{{- end -}}\n{{- if and (ne .Role \"assistant\") $last }}\n<|im_start|>assistant\n{{ end -}}\n{{- end }}\"\"\"\nSYSTEM You are a helpful assistant.\nPARAMETER temperature 0.0001\nLICENSE \"\"\"\n                                 Apache License\n                           Version 2.0, January 2004\n                        http://www.apache.org/licenses/\n\n   TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION\n\n   1. Definitions.\n\n      \"License\" shall mean the terms and conditions for use, reproduction,\n      and distribution as defined by Sections 1 through 9 of this document.\n\n      \"Licensor\" shall mean the copyright owner or entity authorized by\n      the copyright owner that is granting the License.\n\n      \"Legal Entity\" shall mean the union of the acting entity and all\n      other entities that control, are controlled by, or are under common\n      control with that entity. For the purposes of this definition,\n      \"control\" means (i) the power, direct or indirect, to cause the\n      direction or management of such entity, whether by contract or\n      otherwise, or (ii) ownership of fifty percent (50%) or more of the\n      outstanding shares, or (iii) beneficial ownership of such entity.\n\n      \"You\" (or \"Your\") shall mean an individual or Legal Entity\n      exercising permissions granted by this License.\n\n      \"Source\" form shall mean the preferred form for making modifications,\n      including but not limited to software source code, documentation\n      source, and configuration files.\n\n      \"Object\" form shall mean any form resulting from mechanical\n      transformation or translation of a Source form, including but\n      not limited to compiled object code, generated documentation,\n      and conversions to other media types.\n\n      \"Work\" shall mean the work of authorship, whether in Source or\n      Object form, made available under the License, as indicated by a\n      copyright notice that is included in or attached to the work\n      (an example is provided in the Appendix below).\n\n      \"Derivative Works\" shall mean any work, whether in Source or Object\n      form, that is based on (or derived from) the Work and for which the\n      editorial revisions, annotations, elaborations, or other modifications\n      represent, as a whole, an original work of authorship. For the purposes\n      of this License, Derivative Works shall not include works that remain\n      separable from, or merely link (or bind by name) to the interfaces of,\n      the Work and Derivative Works thereof.\n\n      \"Contribution\" shall mean any work of authorship, including\n      the original version of the Work and any modifications or additions\n      to that Work or Derivative Works thereof, that is intentionally\n      submitted to Licensor for inclusion in the Work by the copyright owner\n      or by an individual or Legal Entity authorized to submit on behalf of\n      the copyright owner. For the purposes of this definition, \"submitted\"\n      means any form of electronic, verbal, or written communication sent\n      to the Licensor or its representatives, including but not limited to\n      communication on electronic mailing lists, source code control systems,\n      and issue tracking systems that are managed by, or on behalf of, the\n      Licensor for the purpose of discussing and improving the Work, but\n      excluding communication that is conspicuously marked or otherwise\n      designated in writing by the copyright owner as \"Not a Contribution.\"\n\n      \"Contributor\" shall mean Licensor and any individual or Legal Entity\n      on behalf of whom a Contribution has been received by Licensor and\n      subsequently incorporated within the Work.\n\n   2. Grant of Copyright License. Subject to the terms and conditions of\n      this License, each Contributor hereby grants to You a perpetual,\n      worldwide, non-exclusive, no-charge, royalty-free, irrevocable\n      copyright license to reproduce, prepare Derivative Works of,\n      publicly display, publicly perform, sublicense, and distribute the\n      Work and such Derivative Works in Source or Object form.\n\n   3. Grant of Patent License. Subject to the terms and conditions of\n      this License, each Contributor hereby grants to You a perpetual,\n      worldwide, non-exclusive, no-charge, royalty-free, irrevocable\n      (except as stated in this section) patent license to make, have made,\n      use, offer to sell, sell, import, and otherwise transfer the Work,\n      where such license applies only to those patent claims licensable\n      by such Contributor that are necessarily infringed by their\n      Contribution(s) alone or by combination of their Contribution(s)\n      with the Work to which such Contribution(s) was submitted. If You\n      institute patent litigation against any entity (including a\n      cross-claim or counterclaim in a lawsuit) alleging that the Work\n      or a Contribution incorporated within the Work constitutes direct\n      or contributory patent infringement, then any patent licenses\n      granted to You under this License for that Work shall terminate\n      as of the date such litigation is filed.\n\n   4. Redistribution. You may reproduce and distribute copies of the\n      Work or Derivative Works thereof in any medium, with or without\n      modifications, and in Source or Object form, provided that You\n      meet the following conditions:\n\n      (a) You must give any other recipients of the Work or\n          Derivative Works a copy of this License; and\n\n      (b) You must cause any modified files to carry prominent notices\n          stating that You changed the files; and\n\n      (c) You must retain, in the Source form of any Derivative Works\n          that You distribute, all copyright, patent, trademark, and\n          attribution notices from the Source form of the Work,\n          excluding those notices that do not pertain to any part of\n          the Derivative Works; and\n\n      (d) If the Work includes a \"NOTICE\" text file as part of its\n          distribution, then any Derivative Works that You distribute must\n          include a readable copy of the attribution notices contained\n          within such NOTICE file, excluding those notices that do not\n          pertain to any part of the Derivative Works, in at least one\n          of the following places: within a NOTICE text file distributed\n          as part of the Derivative Works; within the Source form or\n          documentation, if provided along with the Derivative Works; or,\n          within a display generated by the Derivative Works, if and\n          wherever such third-party notices normally appear. The contents\n          of the NOTICE file are for informational purposes only and\n          do not modify the License. You may add Your own attribution\n          notices within Derivative Works that You distribute, alongside\n          or as an addendum to the NOTICE text from the Work, provided\n          that such additional attribution notices cannot be construed\n          as modifying the License.\n\n      You may add Your own copyright statement to Your modifications and\n      may provide additional or different license terms and conditions\n      for use, reproduction, or distribution of Your modifications, or\n      for any such Derivative Works as a whole, provided Your use,\n      reproduction, and distribution of the Work otherwise complies with\n      the conditions stated in this License.\n\n   5. Submission of Contributions. Unless You explicitly state otherwise,\n      any Contribution intentionally submitted for inclusion in the Work\n      by You to the Licensor shall be under the terms and conditions of\n      this License, without any additional terms or conditions.\n      Notwithstanding the above, nothing herein shall supersede or modify\n      the terms of any separate license agreement you may have executed\n      with Licensor regarding such Contributions.\n\n   6. Trademarks. This License does not grant permission to use the trade\n      names, trademarks, service marks, or product names of the Licensor,\n      except as required for reasonable and customary use in describing the\n      origin of the Work and reproducing the content of the NOTICE file.\n\n   7. Disclaimer of Warranty. Unless required by applicable law or\n      agreed to in writing, Licensor provides the Work (and each\n      Contributor provides its Contributions) on an \"AS IS\" BASIS,\n      WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or\n      implied, including, without limitation, any warranties or conditions\n      of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A\n      PARTICULAR PURPOSE. You are solely responsible for determining the\n      appropriateness of using or redistributing the Work and assume any\n      risks associated with Your exercise of permissions under this License.\n\n   8. Limitation of Liability. In no event and under no legal theory,\n      whether in tort (including negligence), contract, or otherwise,\n      unless required by applicable law (such as deliberate and grossly\n      negligent acts) or agreed to in writing, shall any Contributor be\n      liable to You for damages, including any direct, indirect, special,\n      incidental, or consequential damages of any character arising as a\n      result of this License or out of the use or inability to use the\n      Work (including but not limited to damages for loss of goodwill,\n      work stoppage, computer failure or malfunction, or any and all\n      other commercial damages or losses), even if such Contributor\n      has been advised of the possibility of such damages.\n\n   9. Accepting Warranty or Additional Liability. While redistributing\n      the Work or Derivative Works thereof, You may choose to offer,\n      and charge a fee for, acceptance of support, warranty, indemnity,\n      or other liability obligations and/or rights consistent with this\n      License. However, in accepting such obligations, You may act only\n      on Your own behalf and on Your sole responsibility, not on behalf\n      of any other Contributor, and only if You agree to indemnify,\n      defend, and hold each Contributor harmless for any liability\n      incurred by, or claims asserted against, such Contributor by reason\n      of your accepting any such warranty or additional liability.\n\n   END OF TERMS AND CONDITIONS\n\n   APPENDIX: How to apply the Apache License to your work.\n\n      To apply the Apache License to your work, attach the following\n      boilerplate notice, with the fields enclosed by brackets \"[]\"\n      replaced with your own identifying information. (Don't include\n      the brackets!)  The text should be enclosed in the appropriate\n      comment syntax for the file format. We also recommend that a\n      file or class name and description of purpose be included on the\n      same \"printed page\" as the copyright notice for easier\n      identification within third-party archives.\n\n   Copyright 2024 Alibaba Cloud\n\n   Licensed under the Apache License, Version 2.0 (the \"License\");\n   you may not use this file except in compliance with the License.\n   You may obtain a copy of the License at\n\n       http://www.apache.org/licenses/LICENSE-2.0\n\n   Unless required by applicable law or agreed to in writing, software\n   distributed under the License is distributed on an \"AS IS\" BASIS,\n   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n   See the License for the specific language governing permissions and\n   limitations under the License.\"\"\"\n", "parameters": "temperature                    0.0001", "template": "{{- if .System -}}\n<|im_start|>system\n{{ .System }}<|im_end|>\n{{- end -}}\n{{- range $i, $_ := .Messages }}\n{{- $last := eq (len (slice $.Messages $i)) 1 -}}\n{{- if eq .Role \"user\" }}\n<|im_start|>user\n{{ .Content }}<|im_end|>\n{{- else if eq .Role \"assistant\" }}\n<|im_start|>assistant\n{{ if .Content }}{{ .Content }}{{ if not $last }}<|im_end|>\n{{- else -}}<|im_end|>{{- end -}}\n{{- end -}}\n{{- end -}}\n{{- if and (ne .<PERSON> \"assistant\") $last }}\n<|im_start|>assistant\n{{ end -}}\n{{- end }}", "system": "You are a helpful assistant.", "details": {"parent_model": "", "format": "gguf", "family": "qwen25vl", "families": ["qwen25vl"], "parameter_size": "8.3B", "quantization_level": "Q4_K_M"}, "model_info": {"general.architecture": "qwen25vl", "general.file_type": 15, "general.parameter_count": **********, "general.quantization_version": 2, "qwen25vl.attention.head_count": 28, "qwen25vl.attention.head_count_kv": 4, "qwen25vl.attention.layer_norm_rms_epsilon": 1e-06, "qwen25vl.block_count": 28, "qwen25vl.context_length": 128000, "qwen25vl.embedding_length": 3584, "qwen25vl.feed_forward_length": 18944, "qwen25vl.rope.freq_base": 1000000, "qwen25vl.rope.mrope_section": null, "qwen25vl.vision.attention.head_count": 16, "qwen25vl.vision.attention.layer_norm_epsilon": 1e-06, "qwen25vl.vision.block_count": 32, "qwen25vl.vision.embedding_length": 1280, "qwen25vl.vision.fullatt_block_indexes": null, "qwen25vl.vision.num_channels": 3, "qwen25vl.vision.patch_size": 14, "qwen25vl.vision.rope.freq_base": 10000, "qwen25vl.vision.spatial_merge_size": 2, "qwen25vl.vision.spatial_patch_size": 14, "qwen25vl.vision.temporal_patch_size": 2, "qwen25vl.vision.window_size": 112, "tokenizer.ggml.add_eos_token": false, "tokenizer.ggml.add_padding_token": false, "tokenizer.ggml.eos_token_id": 151645, "tokenizer.ggml.merges": null, "tokenizer.ggml.model": "gpt2", "tokenizer.ggml.padding_token_id": 151643, "tokenizer.ggml.pre": "qwen2", "tokenizer.ggml.scores": null, "tokenizer.ggml.token_type": null, "tokenizer.ggml.tokens": null}, "tensors": [{"name": "token_embd.weight", "type": "Q4_K", "shape": [3584, 152064]}, {"name": "blk.0.attn_norm.weight", "type": "F32", "shape": [3584]}, {"name": "blk.0.ffn_down.weight", "type": "Q6_K", "shape": [18944, 3584]}, {"name": "blk.0.ffn_gate.weight", "type": "Q4_K", "shape": [3584, 18944]}, {"name": "blk.0.ffn_up.weight", "type": "Q4_K", "shape": [3584, 18944]}, {"name": "blk.0.ffn_norm.weight", "type": "F32", "shape": [3584]}, {"name": "blk.0.attn_k.bias", "type": "F32", "shape": [512]}, {"name": "blk.0.attn_k.weight", "type": "Q4_K", "shape": [3584, 512]}, {"name": "blk.0.attn_output.weight", "type": "Q4_K", "shape": [3584, 3584]}, {"name": "blk.0.attn_q.bias", "type": "F32", "shape": [3584]}, {"name": "blk.0.attn_q.weight", "type": "Q4_K", "shape": [3584, 3584]}, {"name": "blk.0.attn_v.bias", "type": "F32", "shape": [512]}, {"name": "blk.0.attn_v.weight", "type": "Q6_K", "shape": [3584, 512]}, {"name": "blk.1.attn_norm.weight", "type": "F32", "shape": [3584]}, {"name": "blk.1.ffn_down.weight", "type": "Q6_K", "shape": [18944, 3584]}, {"name": "blk.1.ffn_gate.weight", "type": "Q4_K", "shape": [3584, 18944]}, {"name": "blk.1.ffn_up.weight", "type": "Q4_K", "shape": [3584, 18944]}, {"name": "blk.1.ffn_norm.weight", "type": "F32", "shape": [3584]}, {"name": "blk.1.attn_k.bias", "type": "F32", "shape": [512]}, {"name": "blk.1.attn_k.weight", "type": "Q4_K", "shape": [3584, 512]}, {"name": "blk.1.attn_output.weight", "type": "Q4_K", "shape": [3584, 3584]}, {"name": "blk.1.attn_q.bias", "type": "F32", "shape": [3584]}, {"name": "blk.1.attn_q.weight", "type": "Q4_K", "shape": [3584, 3584]}, {"name": "blk.1.attn_v.bias", "type": "F32", "shape": [512]}, {"name": "blk.1.attn_v.weight", "type": "Q6_K", "shape": [3584, 512]}, {"name": "blk.2.attn_norm.weight", "type": "F32", "shape": [3584]}, {"name": "blk.2.ffn_down.weight", "type": "Q6_K", "shape": [18944, 3584]}, {"name": "blk.2.ffn_gate.weight", "type": "Q4_K", "shape": [3584, 18944]}, {"name": "blk.2.ffn_up.weight", "type": "Q4_K", "shape": [3584, 18944]}, {"name": "blk.2.ffn_norm.weight", "type": "F32", "shape": [3584]}, {"name": "blk.2.attn_k.bias", "type": "F32", "shape": [512]}, {"name": "blk.2.attn_k.weight", "type": "Q4_K", "shape": [3584, 512]}, {"name": "blk.2.attn_output.weight", "type": "Q4_K", "shape": [3584, 3584]}, {"name": "blk.2.attn_q.bias", "type": "F32", "shape": [3584]}, {"name": "blk.2.attn_q.weight", "type": "Q4_K", "shape": [3584, 3584]}, {"name": "blk.2.attn_v.bias", "type": "F32", "shape": [512]}, {"name": "blk.2.attn_v.weight", "type": "Q6_K", "shape": [3584, 512]}, {"name": "blk.3.attn_norm.weight", "type": "F32", "shape": [3584]}, {"name": "blk.3.ffn_norm.weight", "type": "F32", "shape": [3584]}, {"name": "blk.3.attn_k.bias", "type": "F32", "shape": [512]}, {"name": "blk.3.attn_k.weight", "type": "Q4_K", "shape": [3584, 512]}, {"name": "blk.3.attn_output.weight", "type": "Q4_K", "shape": [3584, 3584]}, {"name": "blk.3.attn_q.bias", "type": "F32", "shape": [3584]}, {"name": "blk.3.attn_q.weight", "type": "Q4_K", "shape": [3584, 3584]}, {"name": "blk.3.attn_v.bias", "type": "F32", "shape": [512]}, {"name": "blk.3.attn_v.weight", "type": "Q6_K", "shape": [3584, 512]}, {"name": "blk.3.ffn_down.weight", "type": "Q4_K", "shape": [18944, 3584]}, {"name": "blk.3.ffn_gate.weight", "type": "Q4_K", "shape": [3584, 18944]}, {"name": "blk.3.ffn_up.weight", "type": "Q4_K", "shape": [3584, 18944]}, {"name": "blk.4.attn_norm.weight", "type": "F32", "shape": [3584]}, {"name": "blk.4.ffn_down.weight", "type": "Q4_K", "shape": [18944, 3584]}, {"name": "blk.4.ffn_gate.weight", "type": "Q4_K", "shape": [3584, 18944]}, {"name": "blk.4.ffn_up.weight", "type": "Q4_K", "shape": [3584, 18944]}, {"name": "blk.4.ffn_norm.weight", "type": "F32", "shape": [3584]}, {"name": "blk.4.attn_k.bias", "type": "F32", "shape": [512]}, {"name": "blk.4.attn_k.weight", "type": "Q4_K", "shape": [3584, 512]}, {"name": "blk.4.attn_output.weight", "type": "Q4_K", "shape": [3584, 3584]}, {"name": "blk.4.attn_q.bias", "type": "F32", "shape": [3584]}, {"name": "blk.4.attn_q.weight", "type": "Q4_K", "shape": [3584, 3584]}, {"name": "blk.4.attn_v.bias", "type": "F32", "shape": [512]}, {"name": "blk.4.attn_v.weight", "type": "Q6_K", "shape": [3584, 512]}, {"name": "blk.5.attn_norm.weight", "type": "F32", "shape": [3584]}, {"name": "blk.5.ffn_down.weight", "type": "Q6_K", "shape": [18944, 3584]}, {"name": "blk.5.ffn_gate.weight", "type": "Q4_K", "shape": [3584, 18944]}, {"name": "blk.5.ffn_up.weight", "type": "Q4_K", "shape": [3584, 18944]}, {"name": "blk.5.ffn_norm.weight", "type": "F32", "shape": [3584]}, {"name": "blk.5.attn_k.bias", "type": "F32", "shape": [512]}, {"name": "blk.5.attn_k.weight", "type": "Q4_K", "shape": [3584, 512]}, {"name": "blk.5.attn_output.weight", "type": "Q4_K", "shape": [3584, 3584]}, {"name": "blk.5.attn_q.bias", "type": "F32", "shape": [3584]}, {"name": "blk.5.attn_q.weight", "type": "Q4_K", "shape": [3584, 3584]}, {"name": "blk.5.attn_v.bias", "type": "F32", "shape": [512]}, {"name": "blk.5.attn_v.weight", "type": "Q6_K", "shape": [3584, 512]}, {"name": "blk.6.attn_norm.weight", "type": "F32", "shape": [3584]}, {"name": "blk.6.ffn_down.weight", "type": "Q4_K", "shape": [18944, 3584]}, {"name": "blk.6.ffn_gate.weight", "type": "Q4_K", "shape": [3584, 18944]}, {"name": "blk.6.ffn_up.weight", "type": "Q4_K", "shape": [3584, 18944]}, {"name": "blk.6.ffn_norm.weight", "type": "F32", "shape": [3584]}, {"name": "blk.6.attn_k.bias", "type": "F32", "shape": [512]}, {"name": "blk.6.attn_k.weight", "type": "Q4_K", "shape": [3584, 512]}, {"name": "blk.6.attn_output.weight", "type": "Q4_K", "shape": [3584, 3584]}, {"name": "blk.6.attn_q.bias", "type": "F32", "shape": [3584]}, {"name": "blk.6.attn_q.weight", "type": "Q4_K", "shape": [3584, 3584]}, {"name": "blk.6.attn_v.bias", "type": "F32", "shape": [512]}, {"name": "blk.6.attn_v.weight", "type": "Q6_K", "shape": [3584, 512]}, {"name": "blk.7.attn_norm.weight", "type": "F32", "shape": [3584]}, {"name": "blk.7.ffn_down.weight", "type": "Q4_K", "shape": [18944, 3584]}, {"name": "blk.7.ffn_gate.weight", "type": "Q4_K", "shape": [3584, 18944]}, {"name": "blk.7.ffn_up.weight", "type": "Q4_K", "shape": [3584, 18944]}, {"name": "blk.7.ffn_norm.weight", "type": "F32", "shape": [3584]}, {"name": "blk.7.attn_k.bias", "type": "F32", "shape": [512]}, {"name": "blk.7.attn_k.weight", "type": "Q4_K", "shape": [3584, 512]}, {"name": "blk.7.attn_output.weight", "type": "Q4_K", "shape": [3584, 3584]}, {"name": "blk.7.attn_q.bias", "type": "F32", "shape": [3584]}, {"name": "blk.7.attn_q.weight", "type": "Q4_K", "shape": [3584, 3584]}, {"name": "blk.7.attn_v.bias", "type": "F32", "shape": [512]}, {"name": "blk.7.attn_v.weight", "type": "Q4_K", "shape": [3584, 512]}, {"name": "blk.8.attn_norm.weight", "type": "F32", "shape": [3584]}, {"name": "blk.8.ffn_down.weight", "type": "Q6_K", "shape": [18944, 3584]}, {"name": "blk.8.ffn_gate.weight", "type": "Q4_K", "shape": [3584, 18944]}, {"name": "blk.8.ffn_up.weight", "type": "Q4_K", "shape": [3584, 18944]}, {"name": "blk.8.ffn_norm.weight", "type": "F32", "shape": [3584]}, {"name": "blk.8.attn_k.bias", "type": "F32", "shape": [512]}, {"name": "blk.8.attn_k.weight", "type": "Q4_K", "shape": [3584, 512]}, {"name": "blk.8.attn_output.weight", "type": "Q4_K", "shape": [3584, 3584]}, {"name": "blk.8.attn_q.bias", "type": "F32", "shape": [3584]}, {"name": "blk.8.attn_q.weight", "type": "Q4_K", "shape": [3584, 3584]}, {"name": "blk.8.attn_v.bias", "type": "F32", "shape": [512]}, {"name": "blk.8.attn_v.weight", "type": "Q4_K", "shape": [3584, 512]}, {"name": "blk.9.attn_norm.weight", "type": "F32", "shape": [3584]}, {"name": "blk.9.ffn_down.weight", "type": "Q4_K", "shape": [18944, 3584]}, {"name": "blk.9.ffn_gate.weight", "type": "Q4_K", "shape": [3584, 18944]}, {"name": "blk.9.ffn_up.weight", "type": "Q4_K", "shape": [3584, 18944]}, {"name": "blk.9.ffn_norm.weight", "type": "F32", "shape": [3584]}, {"name": "blk.9.attn_k.bias", "type": "F32", "shape": [512]}, {"name": "blk.9.attn_k.weight", "type": "Q4_K", "shape": [3584, 512]}, {"name": "blk.9.attn_output.weight", "type": "Q4_K", "shape": [3584, 3584]}, {"name": "blk.9.attn_q.bias", "type": "F32", "shape": [3584]}, {"name": "blk.9.attn_q.weight", "type": "Q4_K", "shape": [3584, 3584]}, {"name": "blk.9.attn_v.bias", "type": "F32", "shape": [512]}, {"name": "blk.9.attn_v.weight", "type": "Q6_K", "shape": [3584, 512]}, {"name": "blk.10.attn_norm.weight", "type": "F32", "shape": [3584]}, {"name": "blk.10.ffn_down.weight", "type": "Q4_K", "shape": [18944, 3584]}, {"name": "blk.10.ffn_gate.weight", "type": "Q4_K", "shape": [3584, 18944]}, {"name": "blk.10.ffn_up.weight", "type": "Q4_K", "shape": [3584, 18944]}, {"name": "blk.10.ffn_norm.weight", "type": "F32", "shape": [3584]}, {"name": "blk.10.attn_k.bias", "type": "F32", "shape": [512]}, {"name": "blk.10.attn_k.weight", "type": "Q4_K", "shape": [3584, 512]}, {"name": "blk.10.attn_output.weight", "type": "Q4_K", "shape": [3584, 3584]}, {"name": "blk.10.attn_q.bias", "type": "F32", "shape": [3584]}, {"name": "blk.10.attn_q.weight", "type": "Q4_K", "shape": [3584, 3584]}, {"name": "blk.10.attn_v.bias", "type": "F32", "shape": [512]}, {"name": "blk.10.attn_v.weight", "type": "Q4_K", "shape": [3584, 512]}, {"name": "blk.11.attn_norm.weight", "type": "F32", "shape": [3584]}, {"name": "blk.11.ffn_up.weight", "type": "Q4_K", "shape": [3584, 18944]}, {"name": "blk.11.ffn_norm.weight", "type": "F32", "shape": [3584]}, {"name": "blk.11.attn_k.bias", "type": "F32", "shape": [512]}, {"name": "blk.11.attn_k.weight", "type": "Q4_K", "shape": [3584, 512]}, {"name": "blk.11.attn_output.weight", "type": "Q4_K", "shape": [3584, 3584]}, {"name": "blk.11.attn_q.bias", "type": "F32", "shape": [3584]}, {"name": "blk.11.attn_q.weight", "type": "Q4_K", "shape": [3584, 3584]}, {"name": "blk.11.attn_v.bias", "type": "F32", "shape": [512]}, {"name": "blk.11.attn_v.weight", "type": "Q4_K", "shape": [3584, 512]}, {"name": "blk.11.ffn_down.weight", "type": "Q6_K", "shape": [18944, 3584]}, {"name": "blk.11.ffn_gate.weight", "type": "Q4_K", "shape": [3584, 18944]}, {"name": "blk.12.attn_norm.weight", "type": "F32", "shape": [3584]}, {"name": "blk.12.ffn_down.weight", "type": "Q4_K", "shape": [18944, 3584]}, {"name": "blk.12.ffn_gate.weight", "type": "Q4_K", "shape": [3584, 18944]}, {"name": "blk.12.ffn_up.weight", "type": "Q4_K", "shape": [3584, 18944]}, {"name": "blk.12.ffn_norm.weight", "type": "F32", "shape": [3584]}, {"name": "blk.12.attn_k.bias", "type": "F32", "shape": [512]}, {"name": "blk.12.attn_k.weight", "type": "Q4_K", "shape": [3584, 512]}, {"name": "blk.12.attn_output.weight", "type": "Q4_K", "shape": [3584, 3584]}, {"name": "blk.12.attn_q.bias", "type": "F32", "shape": [3584]}, {"name": "blk.12.attn_q.weight", "type": "Q4_K", "shape": [3584, 3584]}, {"name": "blk.12.attn_v.bias", "type": "F32", "shape": [512]}, {"name": "blk.12.attn_v.weight", "type": "Q6_K", "shape": [3584, 512]}, {"name": "blk.13.attn_norm.weight", "type": "F32", "shape": [3584]}, {"name": "blk.13.ffn_down.weight", "type": "Q4_K", "shape": [18944, 3584]}, {"name": "blk.13.ffn_gate.weight", "type": "Q4_K", "shape": [3584, 18944]}, {"name": "blk.13.ffn_up.weight", "type": "Q4_K", "shape": [3584, 18944]}, {"name": "blk.13.ffn_norm.weight", "type": "F32", "shape": [3584]}, {"name": "blk.13.attn_k.bias", "type": "F32", "shape": [512]}, {"name": "blk.13.attn_k.weight", "type": "Q4_K", "shape": [3584, 512]}, {"name": "blk.13.attn_output.weight", "type": "Q4_K", "shape": [3584, 3584]}, {"name": "blk.13.attn_q.bias", "type": "F32", "shape": [3584]}, {"name": "blk.13.attn_q.weight", "type": "Q4_K", "shape": [3584, 3584]}, {"name": "blk.13.attn_v.bias", "type": "F32", "shape": [512]}, {"name": "blk.13.attn_v.weight", "type": "Q4_K", "shape": [3584, 512]}, {"name": "blk.14.attn_norm.weight", "type": "F32", "shape": [3584]}, {"name": "blk.14.ffn_down.weight", "type": "Q6_K", "shape": [18944, 3584]}, {"name": "blk.14.ffn_gate.weight", "type": "Q4_K", "shape": [3584, 18944]}, {"name": "blk.14.ffn_up.weight", "type": "Q4_K", "shape": [3584, 18944]}, {"name": "blk.14.ffn_norm.weight", "type": "F32", "shape": [3584]}, {"name": "blk.14.attn_k.bias", "type": "F32", "shape": [512]}, {"name": "blk.14.attn_k.weight", "type": "Q4_K", "shape": [3584, 512]}, {"name": "blk.14.attn_output.weight", "type": "Q4_K", "shape": [3584, 3584]}, {"name": "blk.14.attn_q.bias", "type": "F32", "shape": [3584]}, {"name": "blk.14.attn_q.weight", "type": "Q4_K", "shape": [3584, 3584]}, {"name": "blk.14.attn_v.bias", "type": "F32", "shape": [512]}, {"name": "blk.14.attn_v.weight", "type": "Q4_K", "shape": [3584, 512]}, {"name": "blk.15.attn_norm.weight", "type": "F32", "shape": [3584]}, {"name": "blk.15.ffn_down.weight", "type": "Q4_K", "shape": [18944, 3584]}, {"name": "blk.15.ffn_gate.weight", "type": "Q4_K", "shape": [3584, 18944]}, {"name": "blk.15.ffn_up.weight", "type": "Q4_K", "shape": [3584, 18944]}, {"name": "blk.15.ffn_norm.weight", "type": "F32", "shape": [3584]}, {"name": "blk.15.attn_k.bias", "type": "F32", "shape": [512]}, {"name": "blk.15.attn_k.weight", "type": "Q4_K", "shape": [3584, 512]}, {"name": "blk.15.attn_output.weight", "type": "Q4_K", "shape": [3584, 3584]}, {"name": "blk.15.attn_q.bias", "type": "F32", "shape": [3584]}, {"name": "blk.15.attn_q.weight", "type": "Q4_K", "shape": [3584, 3584]}, {"name": "blk.15.attn_v.bias", "type": "F32", "shape": [512]}, {"name": "blk.15.attn_v.weight", "type": "Q6_K", "shape": [3584, 512]}, {"name": "blk.16.attn_norm.weight", "type": "F32", "shape": [3584]}, {"name": "blk.16.ffn_down.weight", "type": "Q4_K", "shape": [18944, 3584]}, {"name": "blk.16.ffn_gate.weight", "type": "Q4_K", "shape": [3584, 18944]}, {"name": "blk.16.ffn_up.weight", "type": "Q4_K", "shape": [3584, 18944]}, {"name": "blk.16.ffn_norm.weight", "type": "F32", "shape": [3584]}, {"name": "blk.16.attn_k.bias", "type": "F32", "shape": [512]}, {"name": "blk.16.attn_k.weight", "type": "Q4_K", "shape": [3584, 512]}, {"name": "blk.16.attn_output.weight", "type": "Q4_K", "shape": [3584, 3584]}, {"name": "blk.16.attn_q.bias", "type": "F32", "shape": [3584]}, {"name": "blk.16.attn_q.weight", "type": "Q4_K", "shape": [3584, 3584]}, {"name": "blk.16.attn_v.bias", "type": "F32", "shape": [512]}, {"name": "blk.16.attn_v.weight", "type": "Q4_K", "shape": [3584, 512]}, {"name": "blk.17.attn_norm.weight", "type": "F32", "shape": [3584]}, {"name": "blk.17.ffn_down.weight", "type": "Q6_K", "shape": [18944, 3584]}, {"name": "blk.17.ffn_gate.weight", "type": "Q4_K", "shape": [3584, 18944]}, {"name": "blk.17.ffn_up.weight", "type": "Q4_K", "shape": [3584, 18944]}, {"name": "blk.17.ffn_norm.weight", "type": "F32", "shape": [3584]}, {"name": "blk.17.attn_k.bias", "type": "F32", "shape": [512]}, {"name": "blk.17.attn_k.weight", "type": "Q4_K", "shape": [3584, 512]}, {"name": "blk.17.attn_output.weight", "type": "Q4_K", "shape": [3584, 3584]}, {"name": "blk.17.attn_q.bias", "type": "F32", "shape": [3584]}, {"name": "blk.17.attn_q.weight", "type": "Q4_K", "shape": [3584, 3584]}, {"name": "blk.17.attn_v.bias", "type": "F32", "shape": [512]}, {"name": "blk.17.attn_v.weight", "type": "Q4_K", "shape": [3584, 512]}, {"name": "blk.18.attn_norm.weight", "type": "F32", "shape": [3584]}, {"name": "blk.18.ffn_down.weight", "type": "Q4_K", "shape": [18944, 3584]}, {"name": "blk.18.ffn_gate.weight", "type": "Q4_K", "shape": [3584, 18944]}, {"name": "blk.18.ffn_up.weight", "type": "Q4_K", "shape": [3584, 18944]}, {"name": "blk.18.ffn_norm.weight", "type": "F32", "shape": [3584]}, {"name": "blk.18.attn_k.bias", "type": "F32", "shape": [512]}, {"name": "blk.18.attn_k.weight", "type": "Q4_K", "shape": [3584, 512]}, {"name": "blk.18.attn_output.weight", "type": "Q4_K", "shape": [3584, 3584]}, {"name": "blk.18.attn_q.bias", "type": "F32", "shape": [3584]}, {"name": "blk.18.attn_q.weight", "type": "Q4_K", "shape": [3584, 3584]}, {"name": "blk.18.attn_v.bias", "type": "F32", "shape": [512]}, {"name": "blk.18.attn_v.weight", "type": "Q6_K", "shape": [3584, 512]}, {"name": "blk.19.attn_norm.weight", "type": "F32", "shape": [3584]}, {"name": "blk.19.ffn_gate.weight", "type": "Q4_K", "shape": [3584, 18944]}, {"name": "blk.19.ffn_up.weight", "type": "Q4_K", "shape": [3584, 18944]}, {"name": "blk.19.ffn_norm.weight", "type": "F32", "shape": [3584]}, {"name": "blk.19.attn_k.bias", "type": "F32", "shape": [512]}, {"name": "blk.19.attn_k.weight", "type": "Q4_K", "shape": [3584, 512]}, {"name": "blk.19.attn_output.weight", "type": "Q4_K", "shape": [3584, 3584]}, {"name": "blk.19.attn_q.bias", "type": "F32", "shape": [3584]}, {"name": "blk.19.attn_q.weight", "type": "Q4_K", "shape": [3584, 3584]}, {"name": "blk.19.attn_v.bias", "type": "F32", "shape": [512]}, {"name": "blk.19.attn_v.weight", "type": "Q4_K", "shape": [3584, 512]}, {"name": "blk.19.ffn_down.weight", "type": "Q4_K", "shape": [18944, 3584]}, {"name": "blk.20.attn_norm.weight", "type": "F32", "shape": [3584]}, {"name": "blk.20.ffn_down.weight", "type": "Q6_K", "shape": [18944, 3584]}, {"name": "blk.20.ffn_gate.weight", "type": "Q4_K", "shape": [3584, 18944]}, {"name": "blk.20.ffn_up.weight", "type": "Q4_K", "shape": [3584, 18944]}, {"name": "blk.20.ffn_norm.weight", "type": "F32", "shape": [3584]}, {"name": "blk.20.attn_k.bias", "type": "F32", "shape": [512]}, {"name": "blk.20.attn_k.weight", "type": "Q4_K", "shape": [3584, 512]}, {"name": "blk.20.attn_output.weight", "type": "Q4_K", "shape": [3584, 3584]}, {"name": "blk.20.attn_q.bias", "type": "F32", "shape": [3584]}, {"name": "blk.20.attn_q.weight", "type": "Q4_K", "shape": [3584, 3584]}, {"name": "blk.20.attn_v.bias", "type": "F32", "shape": [512]}, {"name": "blk.20.attn_v.weight", "type": "Q4_K", "shape": [3584, 512]}, {"name": "blk.21.attn_norm.weight", "type": "F32", "shape": [3584]}, {"name": "blk.21.ffn_down.weight", "type": "Q4_K", "shape": [18944, 3584]}, {"name": "blk.21.ffn_gate.weight", "type": "Q4_K", "shape": [3584, 18944]}, {"name": "blk.21.ffn_up.weight", "type": "Q4_K", "shape": [3584, 18944]}, {"name": "blk.21.ffn_norm.weight", "type": "F32", "shape": [3584]}, {"name": "blk.21.attn_k.bias", "type": "F32", "shape": [512]}, {"name": "blk.21.attn_k.weight", "type": "Q4_K", "shape": [3584, 512]}, {"name": "blk.21.attn_output.weight", "type": "Q4_K", "shape": [3584, 3584]}, {"name": "blk.21.attn_q.bias", "type": "F32", "shape": [3584]}, {"name": "blk.21.attn_q.weight", "type": "Q4_K", "shape": [3584, 3584]}, {"name": "blk.21.attn_v.bias", "type": "F32", "shape": [512]}, {"name": "blk.21.attn_v.weight", "type": "Q6_K", "shape": [3584, 512]}, {"name": "blk.22.attn_norm.weight", "type": "F32", "shape": [3584]}, {"name": "blk.22.ffn_down.weight", "type": "Q4_K", "shape": [18944, 3584]}, {"name": "blk.22.ffn_gate.weight", "type": "Q4_K", "shape": [3584, 18944]}, {"name": "blk.22.ffn_up.weight", "type": "Q4_K", "shape": [3584, 18944]}, {"name": "blk.22.ffn_norm.weight", "type": "F32", "shape": [3584]}, {"name": "blk.22.attn_k.bias", "type": "F32", "shape": [512]}, {"name": "blk.22.attn_k.weight", "type": "Q4_K", "shape": [3584, 512]}, {"name": "blk.22.attn_output.weight", "type": "Q4_K", "shape": [3584, 3584]}, {"name": "blk.22.attn_q.bias", "type": "F32", "shape": [3584]}, {"name": "blk.22.attn_q.weight", "type": "Q4_K", "shape": [3584, 3584]}, {"name": "blk.22.attn_v.bias", "type": "F32", "shape": [512]}, {"name": "blk.22.attn_v.weight", "type": "Q4_K", "shape": [3584, 512]}, {"name": "blk.23.attn_norm.weight", "type": "F32", "shape": [3584]}, {"name": "blk.23.ffn_down.weight", "type": "Q6_K", "shape": [18944, 3584]}, {"name": "blk.23.ffn_gate.weight", "type": "Q4_K", "shape": [3584, 18944]}, {"name": "blk.23.ffn_up.weight", "type": "Q4_K", "shape": [3584, 18944]}, {"name": "blk.23.ffn_norm.weight", "type": "F32", "shape": [3584]}, {"name": "blk.23.attn_k.bias", "type": "F32", "shape": [512]}, {"name": "blk.23.attn_k.weight", "type": "Q4_K", "shape": [3584, 512]}, {"name": "blk.23.attn_output.weight", "type": "Q4_K", "shape": [3584, 3584]}, {"name": "blk.23.attn_q.bias", "type": "F32", "shape": [3584]}, {"name": "blk.23.attn_q.weight", "type": "Q4_K", "shape": [3584, 3584]}, {"name": "blk.23.attn_v.bias", "type": "F32", "shape": [512]}, {"name": "blk.23.attn_v.weight", "type": "Q4_K", "shape": [3584, 512]}, {"name": "blk.24.attn_norm.weight", "type": "F32", "shape": [3584]}, {"name": "blk.24.ffn_down.weight", "type": "Q6_K", "shape": [18944, 3584]}, {"name": "blk.24.ffn_gate.weight", "type": "Q4_K", "shape": [3584, 18944]}, {"name": "blk.24.ffn_up.weight", "type": "Q4_K", "shape": [3584, 18944]}, {"name": "blk.24.ffn_norm.weight", "type": "F32", "shape": [3584]}, {"name": "blk.24.attn_k.bias", "type": "F32", "shape": [512]}, {"name": "blk.24.attn_k.weight", "type": "Q4_K", "shape": [3584, 512]}, {"name": "blk.24.attn_output.weight", "type": "Q4_K", "shape": [3584, 3584]}, {"name": "blk.24.attn_q.bias", "type": "F32", "shape": [3584]}, {"name": "blk.24.attn_q.weight", "type": "Q4_K", "shape": [3584, 3584]}, {"name": "blk.24.attn_v.bias", "type": "F32", "shape": [512]}, {"name": "blk.24.attn_v.weight", "type": "Q6_K", "shape": [3584, 512]}, {"name": "blk.25.attn_norm.weight", "type": "F32", "shape": [3584]}, {"name": "blk.25.ffn_down.weight", "type": "Q6_K", "shape": [18944, 3584]}, {"name": "blk.25.ffn_gate.weight", "type": "Q4_K", "shape": [3584, 18944]}, {"name": "blk.25.ffn_up.weight", "type": "Q4_K", "shape": [3584, 18944]}, {"name": "blk.25.ffn_norm.weight", "type": "F32", "shape": [3584]}, {"name": "blk.25.attn_k.bias", "type": "F32", "shape": [512]}, {"name": "blk.25.attn_k.weight", "type": "Q4_K", "shape": [3584, 512]}, {"name": "blk.25.attn_output.weight", "type": "Q4_K", "shape": [3584, 3584]}, {"name": "blk.25.attn_q.bias", "type": "F32", "shape": [3584]}, {"name": "blk.25.attn_q.weight", "type": "Q4_K", "shape": [3584, 3584]}, {"name": "blk.25.attn_v.bias", "type": "F32", "shape": [512]}, {"name": "blk.25.attn_v.weight", "type": "Q4_K", "shape": [3584, 512]}, {"name": "blk.26.attn_norm.weight", "type": "F32", "shape": [3584]}, {"name": "blk.26.ffn_down.weight", "type": "Q6_K", "shape": [18944, 3584]}, {"name": "blk.26.ffn_gate.weight", "type": "Q4_K", "shape": [3584, 18944]}, {"name": "blk.26.ffn_up.weight", "type": "Q4_K", "shape": [3584, 18944]}, {"name": "blk.26.ffn_norm.weight", "type": "F32", "shape": [3584]}, {"name": "blk.26.attn_k.bias", "type": "F32", "shape": [512]}, {"name": "blk.26.attn_k.weight", "type": "Q4_K", "shape": [3584, 512]}, {"name": "blk.26.attn_output.weight", "type": "Q4_K", "shape": [3584, 3584]}, {"name": "blk.26.attn_q.bias", "type": "F32", "shape": [3584]}, {"name": "blk.26.attn_q.weight", "type": "Q4_K", "shape": [3584, 3584]}, {"name": "blk.26.attn_v.bias", "type": "F32", "shape": [512]}, {"name": "blk.26.attn_v.weight", "type": "Q4_K", "shape": [3584, 512]}, {"name": "blk.27.attn_norm.weight", "type": "F32", "shape": [3584]}, {"name": "blk.27.ffn_down.weight", "type": "Q6_K", "shape": [18944, 3584]}, {"name": "blk.27.ffn_gate.weight", "type": "Q4_K", "shape": [3584, 18944]}, {"name": "blk.27.ffn_up.weight", "type": "Q4_K", "shape": [3584, 18944]}, {"name": "blk.27.ffn_norm.weight", "type": "F32", "shape": [3584]}, {"name": "blk.27.attn_k.bias", "type": "F32", "shape": [512]}, {"name": "blk.27.attn_k.weight", "type": "Q4_K", "shape": [3584, 512]}, {"name": "blk.27.attn_output.weight", "type": "Q4_K", "shape": [3584, 3584]}, {"name": "blk.27.attn_q.bias", "type": "F32", "shape": [3584]}, {"name": "blk.27.attn_q.weight", "type": "Q4_K", "shape": [3584, 3584]}, {"name": "blk.27.attn_v.bias", "type": "F32", "shape": [512]}, {"name": "blk.27.attn_v.weight", "type": "Q6_K", "shape": [3584, 512]}, {"name": "v.blk.0.attn_out.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.0.attn_out.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.0.attn_q.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.0.attn_k.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.0.attn_v.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.0.attn_q.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.0.attn_k.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.0.attn_v.weight", "type": "Q4_K", "shape": [1280, 1280]}, {"name": "v.blk.0.ffn_down.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.0.ffn_down.weight", "type": "F16", "shape": [3420, 1280]}, {"name": "v.blk.0.ffn_gate.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.0.ffn_gate.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.0.ffn_up.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.0.ffn_up.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.0.ln1.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.0.ln2.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.1.attn_out.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.1.attn_out.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.1.attn_q.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.1.attn_k.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.1.attn_v.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.1.attn_q.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.1.attn_k.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.1.attn_v.weight", "type": "Q4_K", "shape": [1280, 1280]}, {"name": "v.blk.1.ffn_down.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.1.ffn_down.weight", "type": "F16", "shape": [3420, 1280]}, {"name": "v.blk.1.ffn_gate.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.1.ffn_gate.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.1.ffn_up.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.1.ffn_up.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.1.ln1.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.1.ln2.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.10.attn_out.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.10.attn_out.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.10.attn_q.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.10.attn_k.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.10.attn_v.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.10.attn_q.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.10.attn_k.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.10.attn_v.weight", "type": "Q6_K", "shape": [1280, 1280]}, {"name": "v.blk.10.ffn_down.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.10.ffn_down.weight", "type": "F16", "shape": [3420, 1280]}, {"name": "v.blk.10.ffn_gate.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.10.ffn_gate.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.10.ffn_up.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.10.ffn_up.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.10.ln1.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.10.ln2.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.11.attn_out.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.11.attn_out.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.11.attn_q.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.11.attn_k.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.11.attn_v.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.11.attn_q.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.11.attn_k.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.11.attn_v.weight", "type": "Q4_K", "shape": [1280, 1280]}, {"name": "v.blk.11.ffn_down.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.11.ffn_down.weight", "type": "F16", "shape": [3420, 1280]}, {"name": "v.blk.11.ffn_gate.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.11.ffn_gate.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.11.ffn_up.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.11.ffn_up.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.11.ln1.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.11.ln2.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.12.attn_out.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.12.attn_out.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.12.attn_q.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.12.attn_k.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.12.attn_v.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.12.attn_q.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.12.attn_k.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.12.attn_v.weight", "type": "Q4_K", "shape": [1280, 1280]}, {"name": "v.blk.12.ffn_down.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.12.ffn_down.weight", "type": "F16", "shape": [3420, 1280]}, {"name": "v.blk.12.ffn_gate.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.12.ffn_gate.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.12.ffn_up.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.12.ffn_up.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.12.ln1.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.12.ln2.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.13.attn_out.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.13.attn_out.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.13.attn_q.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.13.attn_k.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.13.attn_v.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.13.attn_q.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.13.attn_k.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.13.attn_v.weight", "type": "Q6_K", "shape": [1280, 1280]}, {"name": "v.blk.13.ffn_down.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.13.ffn_down.weight", "type": "F16", "shape": [3420, 1280]}, {"name": "v.blk.13.ffn_gate.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.13.ffn_gate.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.13.ffn_up.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.13.ffn_up.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.13.ln1.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.13.ln2.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.14.attn_out.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.14.attn_out.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.14.attn_q.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.14.attn_k.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.14.attn_v.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.14.attn_q.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.14.attn_k.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.14.attn_v.weight", "type": "Q4_K", "shape": [1280, 1280]}, {"name": "v.blk.14.ffn_down.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.14.ffn_down.weight", "type": "F16", "shape": [3420, 1280]}, {"name": "v.blk.14.ffn_gate.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.14.ffn_gate.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.14.ffn_up.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.14.ffn_up.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.14.ln1.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.14.ln2.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.15.attn_out.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.15.attn_out.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.15.attn_q.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.15.attn_k.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.15.attn_v.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.15.attn_q.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.15.attn_k.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.15.attn_v.weight", "type": "Q4_K", "shape": [1280, 1280]}, {"name": "v.blk.15.ffn_down.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.15.ffn_down.weight", "type": "F16", "shape": [3420, 1280]}, {"name": "v.blk.15.ffn_gate.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.15.ffn_gate.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.15.ffn_up.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.15.ffn_up.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.15.ln1.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.15.ln2.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.16.attn_out.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.16.attn_out.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.16.attn_q.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.16.attn_k.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.16.attn_v.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.16.attn_q.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.16.attn_k.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.16.attn_v.weight", "type": "Q6_K", "shape": [1280, 1280]}, {"name": "v.blk.16.ffn_down.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.16.ffn_down.weight", "type": "F16", "shape": [3420, 1280]}, {"name": "v.blk.16.ffn_gate.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.16.ffn_gate.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.16.ffn_up.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.16.ffn_up.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.16.ln1.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.16.ln2.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.17.attn_out.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.17.attn_out.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.17.attn_q.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.17.attn_k.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.17.attn_v.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.17.attn_q.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.17.attn_k.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.17.attn_v.weight", "type": "Q4_K", "shape": [1280, 1280]}, {"name": "v.blk.17.ffn_down.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.17.ffn_down.weight", "type": "F16", "shape": [3420, 1280]}, {"name": "v.blk.17.ffn_gate.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.17.ffn_gate.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.17.ffn_up.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.17.ffn_up.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.17.ln1.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.17.ln2.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.18.attn_out.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.18.attn_out.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.18.attn_q.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.18.attn_k.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.18.attn_v.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.18.attn_q.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.18.attn_k.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.18.attn_v.weight", "type": "Q4_K", "shape": [1280, 1280]}, {"name": "v.blk.18.ffn_down.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.18.ffn_down.weight", "type": "F16", "shape": [3420, 1280]}, {"name": "v.blk.18.ffn_gate.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.18.ffn_gate.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.18.ffn_up.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.18.ffn_up.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.18.ln1.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.18.ln2.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.19.attn_out.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.19.attn_out.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.19.attn_q.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.19.attn_k.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.19.attn_v.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.19.attn_q.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.19.attn_k.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.19.attn_v.weight", "type": "Q6_K", "shape": [1280, 1280]}, {"name": "v.blk.19.ffn_down.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.19.ffn_down.weight", "type": "F16", "shape": [3420, 1280]}, {"name": "v.blk.19.ffn_gate.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.19.ffn_gate.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.19.ffn_up.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.19.ffn_up.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.19.ln1.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.19.ln2.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.2.attn_out.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.2.attn_out.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.2.attn_q.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.2.attn_k.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.2.attn_v.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.2.attn_q.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.2.attn_k.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.2.attn_v.weight", "type": "Q4_K", "shape": [1280, 1280]}, {"name": "v.blk.2.ffn_down.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.2.ffn_down.weight", "type": "F16", "shape": [3420, 1280]}, {"name": "v.blk.2.ffn_gate.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.2.ffn_gate.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.2.ffn_up.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.2.ffn_up.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.2.ln1.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.2.ln2.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.20.attn_out.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.20.attn_out.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.20.attn_q.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.20.attn_k.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.20.attn_v.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.20.attn_q.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.20.attn_k.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.20.attn_v.weight", "type": "Q4_K", "shape": [1280, 1280]}, {"name": "v.blk.20.ffn_down.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.20.ffn_down.weight", "type": "F16", "shape": [3420, 1280]}, {"name": "v.blk.20.ffn_gate.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.20.ffn_gate.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.20.ffn_up.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.20.ffn_up.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.20.ln1.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.20.ln2.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.21.attn_out.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.21.attn_out.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.21.attn_q.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.21.attn_k.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.21.attn_v.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.21.attn_q.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.21.attn_k.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.21.attn_v.weight", "type": "Q6_K", "shape": [1280, 1280]}, {"name": "v.blk.21.ffn_down.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.21.ffn_down.weight", "type": "F16", "shape": [3420, 1280]}, {"name": "v.blk.21.ffn_gate.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.21.ffn_gate.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.21.ffn_up.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.21.ffn_up.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.21.ln1.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.21.ln2.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.22.attn_out.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.22.attn_out.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.22.attn_q.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.22.attn_k.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.22.attn_v.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.22.attn_q.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.22.attn_k.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.22.attn_v.weight", "type": "Q4_K", "shape": [1280, 1280]}, {"name": "v.blk.22.ffn_down.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.22.ffn_down.weight", "type": "F16", "shape": [3420, 1280]}, {"name": "v.blk.22.ffn_gate.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.22.ffn_gate.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.22.ffn_up.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.22.ffn_up.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.22.ln1.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.22.ln2.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.23.attn_out.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.23.attn_out.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.23.attn_q.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.23.attn_k.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.23.attn_v.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.23.attn_q.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.23.attn_k.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.23.attn_v.weight", "type": "Q4_K", "shape": [1280, 1280]}, {"name": "v.blk.23.ffn_down.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.23.ffn_down.weight", "type": "F16", "shape": [3420, 1280]}, {"name": "v.blk.23.ffn_gate.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.23.ffn_gate.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.23.ffn_up.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.23.ffn_up.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.23.ln1.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.23.ln2.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.24.attn_out.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.24.attn_out.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.24.attn_q.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.24.attn_k.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.24.attn_v.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.24.attn_q.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.24.attn_k.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.24.attn_v.weight", "type": "Q6_K", "shape": [1280, 1280]}, {"name": "v.blk.24.ffn_down.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.24.ffn_down.weight", "type": "F16", "shape": [3420, 1280]}, {"name": "v.blk.24.ffn_gate.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.24.ffn_gate.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.24.ffn_up.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.24.ffn_up.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.24.ln1.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.24.ln2.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.25.attn_out.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.25.attn_out.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.25.attn_q.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.25.attn_k.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.25.attn_v.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.25.attn_q.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.25.attn_k.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.25.attn_v.weight", "type": "Q4_K", "shape": [1280, 1280]}, {"name": "v.blk.25.ffn_down.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.25.ffn_down.weight", "type": "F16", "shape": [3420, 1280]}, {"name": "v.blk.25.ffn_gate.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.25.ffn_gate.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.25.ffn_up.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.25.ffn_up.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.25.ln1.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.25.ln2.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.26.attn_out.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.26.attn_out.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.26.attn_q.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.26.attn_k.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.26.attn_v.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.26.attn_q.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.26.attn_k.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.26.attn_v.weight", "type": "Q4_K", "shape": [1280, 1280]}, {"name": "v.blk.26.ffn_down.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.26.ffn_down.weight", "type": "F16", "shape": [3420, 1280]}, {"name": "v.blk.26.ffn_gate.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.26.ffn_gate.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.26.ffn_up.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.26.ffn_up.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.26.ln1.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.26.ln2.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.27.attn_out.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.27.attn_out.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.27.attn_q.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.27.attn_k.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.27.attn_v.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.27.attn_q.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.27.attn_k.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.27.attn_v.weight", "type": "Q6_K", "shape": [1280, 1280]}, {"name": "v.blk.27.ffn_down.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.27.ffn_down.weight", "type": "F16", "shape": [3420, 1280]}, {"name": "v.blk.27.ffn_gate.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.27.ffn_gate.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.27.ffn_up.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.27.ffn_up.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.27.ln1.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.27.ln2.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.28.attn_out.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.28.attn_out.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.28.attn_q.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.28.attn_k.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.28.attn_v.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.28.attn_q.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.28.attn_k.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.28.attn_v.weight", "type": "Q4_K", "shape": [1280, 1280]}, {"name": "v.blk.28.ffn_down.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.28.ffn_down.weight", "type": "F16", "shape": [3420, 1280]}, {"name": "v.blk.28.ffn_gate.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.28.ffn_gate.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.28.ffn_up.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.28.ffn_up.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.28.ln1.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.28.ln2.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.29.attn_out.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.29.attn_out.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.29.attn_q.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.29.attn_k.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.29.attn_v.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.29.attn_q.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.29.attn_k.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.29.attn_v.weight", "type": "Q4_K", "shape": [1280, 1280]}, {"name": "v.blk.29.ffn_down.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.29.ffn_down.weight", "type": "F16", "shape": [3420, 1280]}, {"name": "v.blk.29.ffn_gate.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.29.ffn_gate.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.29.ffn_up.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.29.ffn_up.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.29.ln1.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.29.ln2.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.3.attn_out.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.3.attn_out.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.3.attn_q.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.3.attn_k.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.3.attn_v.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.3.attn_q.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.3.attn_k.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.3.attn_v.weight", "type": "Q6_K", "shape": [1280, 1280]}, {"name": "v.blk.3.ffn_down.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.3.ffn_down.weight", "type": "F16", "shape": [3420, 1280]}, {"name": "v.blk.3.ffn_gate.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.3.ffn_gate.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.3.ffn_up.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.3.ffn_up.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.3.ln1.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.3.ln2.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.30.attn_out.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.30.attn_out.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.30.attn_q.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.30.attn_k.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.30.attn_v.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.30.attn_q.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.30.attn_k.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.30.attn_v.weight", "type": "Q6_K", "shape": [1280, 1280]}, {"name": "v.blk.30.ffn_down.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.30.ffn_down.weight", "type": "F16", "shape": [3420, 1280]}, {"name": "v.blk.30.ffn_gate.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.30.ffn_gate.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.30.ffn_up.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.30.ffn_up.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.30.ln1.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.30.ln2.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.31.attn_out.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.31.attn_out.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.31.attn_q.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.31.attn_k.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.31.attn_v.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.31.attn_q.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.31.attn_k.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.31.attn_v.weight", "type": "Q6_K", "shape": [1280, 1280]}, {"name": "v.blk.31.ffn_down.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.31.ffn_down.weight", "type": "F16", "shape": [3420, 1280]}, {"name": "v.blk.31.ffn_gate.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.31.ffn_gate.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.31.ffn_up.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.31.ffn_up.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.31.ln1.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.31.ln2.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.4.attn_out.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.4.attn_out.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.4.attn_q.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.4.attn_k.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.4.attn_v.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.4.attn_q.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.4.attn_k.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.4.attn_v.weight", "type": "Q6_K", "shape": [1280, 1280]}, {"name": "v.blk.4.ffn_down.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.4.ffn_down.weight", "type": "F16", "shape": [3420, 1280]}, {"name": "v.blk.4.ffn_gate.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.4.ffn_gate.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.4.ffn_up.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.4.ffn_up.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.4.ln1.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.4.ln2.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.5.attn_out.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.5.attn_out.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.5.attn_q.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.5.attn_k.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.5.attn_v.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.5.attn_q.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.5.attn_k.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.5.attn_v.weight", "type": "Q6_K", "shape": [1280, 1280]}, {"name": "v.blk.5.ffn_down.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.5.ffn_down.weight", "type": "F16", "shape": [3420, 1280]}, {"name": "v.blk.5.ffn_gate.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.5.ffn_gate.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.5.ffn_up.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.5.ffn_up.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.5.ln1.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.5.ln2.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.6.attn_out.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.6.attn_out.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.6.attn_q.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.6.attn_k.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.6.attn_v.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.6.attn_q.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.6.attn_k.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.6.attn_v.weight", "type": "Q6_K", "shape": [1280, 1280]}, {"name": "v.blk.6.ffn_down.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.6.ffn_down.weight", "type": "F16", "shape": [3420, 1280]}, {"name": "v.blk.6.ffn_gate.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.6.ffn_gate.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.6.ffn_up.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.6.ffn_up.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.6.ln1.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.6.ln2.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.7.attn_out.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.7.attn_out.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.7.attn_q.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.7.attn_k.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.7.attn_v.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.7.attn_q.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.7.attn_k.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.7.attn_v.weight", "type": "Q6_K", "shape": [1280, 1280]}, {"name": "v.blk.7.ffn_down.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.7.ffn_down.weight", "type": "F16", "shape": [3420, 1280]}, {"name": "v.blk.7.ffn_gate.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.7.ffn_gate.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.7.ffn_up.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.7.ffn_up.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.7.ln1.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.7.ln2.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.8.attn_out.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.8.attn_out.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.8.attn_q.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.8.attn_k.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.8.attn_v.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.8.attn_q.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.8.attn_k.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.8.attn_v.weight", "type": "Q6_K", "shape": [1280, 1280]}, {"name": "v.blk.8.ffn_down.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.8.ffn_down.weight", "type": "F16", "shape": [3420, 1280]}, {"name": "v.blk.8.ffn_gate.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.8.ffn_gate.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.8.ffn_up.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.8.ffn_up.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.8.ln1.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.8.ln2.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.9.attn_out.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.9.attn_out.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.9.attn_q.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.9.attn_k.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.9.attn_v.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.9.attn_q.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.9.attn_k.weight", "type": "F16", "shape": [1280, 1280]}, {"name": "v.blk.9.attn_v.weight", "type": "Q6_K", "shape": [1280, 1280]}, {"name": "v.blk.9.ffn_down.bias", "type": "F32", "shape": [1280]}, {"name": "v.blk.9.ffn_down.weight", "type": "F16", "shape": [3420, 1280]}, {"name": "v.blk.9.ffn_gate.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.9.ffn_gate.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.9.ffn_up.bias", "type": "F32", "shape": [3420]}, {"name": "v.blk.9.ffn_up.weight", "type": "F16", "shape": [1280, 3420]}, {"name": "v.blk.9.ln1.weight", "type": "F32", "shape": [1280]}, {"name": "v.blk.9.ln2.weight", "type": "F32", "shape": [1280]}, {"name": "v.merger.ln_q.weight", "type": "F32", "shape": [1280]}, {"name": "v.merger.mlp.0.bias", "type": "F32", "shape": [5120]}, {"name": "v.merger.mlp.0.weight", "type": "F16", "shape": [5120, 5120]}, {"name": "v.merger.mlp.2.bias", "type": "F32", "shape": [3584]}, {"name": "v.merger.mlp.2.weight", "type": "F16", "shape": [5120, 3584]}, {"name": "v.patch_embd_0.weight", "type": "F16", "shape": [14, 14, 3, 1280]}, {"name": "v.patch_embd_1.weight", "type": "F16", "shape": [14, 14, 3, 1280]}, {"name": "output_norm.weight", "type": "F32", "shape": [3584]}, {"name": "output.weight", "type": "Q6_K", "shape": [3584, 152064]}], "capabilities": ["completion", "vision"], "modified_at": "2025-08-01T10:12:40.9954207+08:00"}